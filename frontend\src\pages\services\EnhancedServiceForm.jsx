import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService, masterDataAPI } from '../../services/api';
import onlineCallTypeAPI from '../../services/onlineCallTypeAPI';
import SearchableSelect from '../../components/ui/SearchableSelect';
import MapLocationPicker from '../../components/ui/MapLocationPicker';
import CustomerFeedbackModal from '../../components/modals/CustomerFeedbackModal';
import NewCustomerModal from '../../components/modals/NewCustomerModal';
import MobileInput from '../../components/ui/MobileInput';
import { hasPhoneDigits, validationRules } from '../../utils/validation';
import { useCustomerSearch } from '../../hooks/useCustomerSearch';
import {
  FaSave,
  FaTimes,
  FaUser,
  FaBuilding,
  FaPhone,
  FaMapMarkerAlt,
  FaClock,
  FaRupeeSign,
  FaCalendar,
  FaTools,
  FaComments,
  FaCheckCircle,
  FaPlus
} from 'react-icons/fa';

const EnhancedServiceForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [statusUpdating, setStatusUpdating] = useState(false);
  const [showMapPicker, setShowMapPicker] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [showNewCustomerModal, setShowNewCustomerModal] = useState(false);

  // Master data states
  const [customers, setCustomers] = useState([]);
  const [executives, setExecutives] = useState([]);
  const [typeOfCalls, setTypeOfCalls] = useState([]);
  const [onlineCallTypes, setOnlineCallTypes] = useState([]);
  const [productsIssues, setProductsIssues] = useState([]);
  const [callStatuses, setCallStatuses] = useState([]);
  const [designations, setDesignations] = useState([]);
  const [tallyProducts, setTallyProducts] = useState([]);
  const [selectedCustomerAMC, setSelectedCustomerAMC] = useState(null);

  // Form data state - Simplified for lead management (create) / Comprehensive for edit
  const [formData, setFormData] = useState({
    // Mandatory Fields
    serviceNumber: '',
    customerId: '',
    customerName: '',
    contactNumber: '',

    // Optional Fields
    executiveId: '',
    typeOfCallId: '', // Call Issue/Type from Products/Issues master
    companyName: '',
    tallySerialNumber: '',
    serviceLocation: '', // Map Location
    locationCoordinates: { lat: '', lng: '' },
    googleMapsLink: '',
    serviceCharges: '',
    scheduledDate: new Date().toISOString().slice(0, 10), // Scheduled service date - defaults to today

    // Auto-populated fields (editable after auto-fetch)
    emailAddress: '',
    designation: '',
    tallyVersion: '',
    tssStatus: '',
    tssExpiry: '',

    // Executive auto-populated fields (editable after auto-fetch)
    executivePhone: '',
    executiveEmail: '',

    // Internal fields for status management
    statusId: '', // Will be set automatically based on customer type
    isLead: false, // Track if this is a lead (no Tally Serial Number)

    // Additional fields for comprehensive edit mode
    serviceType: 'onsite', // onsite/online
    priority: 'medium', // low/medium/high/critical
    subject: '',
    description: '',
    callStartTime: '',
    callEndTime: '',
    estimatedHours: 0,
    actualHours: 0,
    billableHours: 0,
    contactPerson: '',
    currency: 'INR',
    chargingType: 'fixed', // fixed/hourly/daily
    paymentStatus: 'pending', // pending/paid/partial/cancelled
    hourlyRate: '',
    totalAmount: '',
    executiveRemarks: '',
    internalNotes: '',
    customerReportedIssue: '',
    actualIssueFound: '',
    solutionProvided: '',
    customerFeedback: '',
    customerSatisfaction: 5,
    followUpRequired: false,
    followUpDate: '',
    followUpNotes: '',

    // Call Type (Free Call, AMC Call, Per Call)
    callType: '', // New field for Free Call, AMC Call, Per Call
  });

  const [errors, setErrors] = useState({});

  // Customer search hook for server-side search
  const { searchResults, isSearching, searchCustomers, resetSearch } = useCustomerSearch();

  // Helper function to get user-friendly field labels
  const getFieldLabel = (fieldName) => {
    const labels = {
      serviceNumber: 'Service Number',
      customerId: 'Customer Name',
      executiveId: 'Executive Name',
      serviceType: 'Service Type',
      typeOfCallId: 'Type of Call (Onsite)',
      onlineCallTypeId: 'Type of Call (Online)',
      serviceLocation: 'Service Location',
      bookingDate: 'Booking Date',
      tallySerialNumber: 'Tally Serial Number',
      contactNumber: 'Contact Number',
      companyName: 'Company Name',
      tallyVersion: 'Tally Version',
      designation: 'Designation',
      executivePhone: 'Executive Phone',
      executiveEmail: 'Executive Email',
      callStartTime: 'Call Start Time',
      callEndTime: 'Call End Time',
      statusId: 'Status',
      chargingType: 'Charging Type',
      serviceCharges: 'Service Charges',
      hourlyRate: 'Hourly Rate',
      executiveRemarks: 'Executive Remarks',
      contactPerson: 'Contact Person',
      onlineExecutiveId: 'Executive Name (Online)',
      onlineStatusId: 'Status (Online)',
      startTime: 'Start Time',
      endTime: 'End Time',
      mobileNumber2: 'Mobile Number 2',
      remarks: 'Remarks',
      onlineExecutiveRemarks: 'Executive Remarks (Online)'
    };
    return labels[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  };

  // Generate service number for new services
  useEffect(() => {
    if (!isEdit) {
      generateServiceNumber();
      setFormData(prev => ({
        ...prev,
        callStartTime: new Date().toISOString().slice(0, 16), // Current timestamp
        scheduledDate: new Date().toISOString().slice(0, 10) // Default to today's date
      }));
    }
  }, [isEdit]);

  // Force re-render of conditional sections when serviceType changes
  // This ensures sections are visible when editing existing services
  useEffect(() => {
    // This effect will trigger whenever formData.serviceType changes
    // ensuring conditional sections are properly displayed
    console.log('🔍 Service type changed to:', formData.serviceType, {
      isEdit,
      hasServiceType: !!formData.serviceType,
      shouldShowOnsiteSection: formData.serviceType === 'onsite',
      shouldShowOnlineSection: formData.serviceType === 'online',
      formDataKeys: Object.keys(formData).filter(key => formData[key] !== '' && formData[key] !== null && formData[key] !== undefined)
    });

    // Force a small re-render to ensure conditional sections update
    if (isEdit && formData.serviceType) {
      // This timeout ensures the DOM has time to update
      setTimeout(() => {
        console.log('🔍 Section visibility check after serviceType change:', {
          onsiteSection: document.querySelector('[data-section="onsite"]') ? 'visible' : 'hidden',
          onlineSection: document.querySelector('[data-section="online"]') ? 'visible' : 'hidden'
        });
      }, 100);
    }
  }, [formData.serviceType, isEdit]);

  // Generate next service number in SER-001 format
  const generateServiceNumber = async () => {
    try {
      const response = await apiService.get('/service-calls?limit=10&sortBy=created_at&sortOrder=DESC');
      const lastServiceCall = response.data?.data?.serviceCalls?.[0];

      let nextNumber = 1;
      if (lastServiceCall?.call_number) {
        // Extract number from formats like SER-001, SC000001, etc.
        const match = lastServiceCall.call_number.match(/(\d+)$/);
        if (match) {
          nextNumber = parseInt(match[1]) + 1;
        }
      }

      const serviceNumber = `SER-${String(nextNumber).padStart(3, '0')}`;
      setFormData(prev => ({ ...prev, serviceNumber }));

      console.log('🔍 Generated Service Number:', serviceNumber);
    } catch (error) {
      console.error('Error generating service number:', error);
      // Fallback to timestamp-based number
      const serviceNumber = `SER-${String(Date.now()).slice(-3)}`;
      setFormData(prev => ({ ...prev, serviceNumber }));
    }
  };

  // Fetch master data
  useEffect(() => {
    fetchMasterData();
    if (isEdit) {
      fetchServiceData();
    }
  }, [isEdit, id]);

  const fetchMasterData = async () => {
    try {
      setLoading(true);

      // First check if master data tables exist
      try {
        const debugRes = await apiService.get('/master-data/debug');
        console.log('🔍 Master Data Debug Info:', debugRes.data);
      } catch (debugError) {
        console.warn('🔍 Debug endpoint not available:', debugError.message);
      }

      const [
        customersRes,
        executivesRes,
        typeOfCallsRes,
        onlineCallTypesRes,
        productsIssuesRes,
        designationsRes,
        tallyProductsRes
      ] = await Promise.all([
        apiService.get('/customers'),
        masterDataAPI.getExecutives(),
        masterDataAPI.getTypeOfCalls(),
        onlineCallTypeAPI.getActiveForDropdown(),
        apiService.get('/master-data/products-issues/search?limit=100'),
        masterDataAPI.getDesignations(),
        masterDataAPI.getTallyProducts()
      ]);

      // Fetch call statuses from master data API instead of using hardcoded values
      let callStatusesRes;
      try {
        callStatusesRes = await masterDataAPI.getCallStatuses();
        console.log('✅ Successfully fetched call statuses from API:', callStatusesRes.data);
      } catch (error) {
        console.error('❌ Failed to fetch call statuses from API:', error);
        // Fallback to hardcoded statuses only if API fails
        callStatusesRes = {
          data: {
            success: true,
            data: {
              callstatus: [
                { id: 'fallback-pending-uuid', name: 'Pending', code: 'PENDING' },
                { id: 'fallback-progress-uuid', name: 'In Progress', code: 'IN_PROGRESS' },
                { id: 'fallback-completed-uuid', name: 'Completed', code: 'COMPLETED' }
              ]
            }
          }
        };
        console.log('⚠️ Using fallback call statuses due to API error');
      }

      const customers = customersRes.data?.data?.customers || [];
      const executives = executivesRes.data?.data?.executives || [];
      const typeOfCalls = typeOfCallsRes.data?.data?.typeOfCalls || typeOfCallsRes.data?.data?.typeofcall || [];
      const onlineCallTypes = onlineCallTypesRes || []; // Already formatted by API
      const productsIssues = productsIssuesRes.data?.data || []; // Products/Issues data
      const callStatuses = callStatusesRes.data?.data?.callstatus || [];
      const designations = designationsRes.data?.data?.designations || designationsRes.data?.data?.designation || [];
      const tallyProducts = tallyProductsRes.data?.data?.tallyproduct || [];

      // Debug logging for master data
      console.log('🔍 Raw API Responses:', {
        customersRes: customersRes.data,
        executivesRes: executivesRes.data,
        typeOfCallsRes: typeOfCallsRes.data,
        callStatusesRes: callStatusesRes.data,
        designationsRes: designationsRes.data,
        productsIssuesRes: productsIssuesRes.data
      });

      console.log('🔍 Master Data Debug:', {
        customers: customers.length,
        executives: executives.length,
        typeOfCalls: typeOfCalls.length,
        onlineCallTypes: onlineCallTypes.length,
        productsIssues: productsIssues.length,
        callStatuses: callStatuses.length,
        designations: designations.length,
        tallyProducts: tallyProducts.length
      });

      console.log('🔍 Type of Calls Sample:', typeOfCalls.slice(0, 3));
      console.log('🔍 Call Statuses Sample:', callStatuses.slice(0, 3));
      console.log('🔍 Executives Sample:', executives.slice(0, 3));
      console.log('🔍 Customers Sample:', customers.slice(0, 3));
      console.log('🔍 Products/Issues Sample:', productsIssues.slice(0, 5));
      console.log('🔍 Online Call Types Sample:', onlineCallTypes.slice(0, 3));

      setCustomers(customers);
      setExecutives(executives);
      setTypeOfCalls(typeOfCalls);
      setOnlineCallTypes(onlineCallTypes);
      setProductsIssues(productsIssues);
      setCallStatuses(callStatuses);
      setDesignations(designations);
      setTallyProducts(tallyProducts);

      // Set default status for new service creation
      if (!isEdit && !formData.statusId && callStatuses.length > 0) {
        const defaultStatus = callStatuses.find(status =>
          status.code === 'PENDING' ||
          status.name.toLowerCase() === 'pending'
        );

        if (defaultStatus) {
          setFormData(prev => ({
            ...prev,
            statusId: defaultStatus.id
          }));
          console.log('🔄 Default status set to "Pending" for new service:', defaultStatus);
        }
      }
    } catch (error) {
      console.error('Error fetching master data:', error);
      console.error('🔍 Master Data Error Details:', {
        typeOfCallsError: typeOfCallsRes?.data || typeOfCallsRes?.error,
        callStatusesError: callStatusesRes?.data || callStatusesRes?.error,
        executivesError: executivesRes?.data || executivesRes?.error
      });
      toast.error('Failed to load form data. Check console for details.');
    } finally {
      setLoading(false);
    }
  };

  const fetchServiceData = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/service-calls/${id}`);
      if (response.data?.success && response.data?.data?.serviceCall) {
        const { serviceCall } = response.data.data;

        console.log('🔍 Fetched service call data for edit:', serviceCall);

        // Map backend service call data to frontend form structure
        const mappedData = {
          // Basic service information
          serviceNumber: serviceCall.call_number || '',
          customerId: serviceCall.customer_id || '',
          customerName: serviceCall.customer?.company_name || serviceCall.customer?.name || '',
          companyName: serviceCall.customer?.company_name || '',
          contactNumber: serviceCall.customer?.phone || '',
          emailAddress: serviceCall.customer?.email || '',
          tallySerialNumber: serviceCall.customer?.tally_serial_number || '',

          // Service details - FIXED: Map call_type to serviceType for conditional rendering
          typeOfCallId: serviceCall.products_issues_id || serviceCall.type_of_call_id || '',
          serviceType: serviceCall.call_type || 'onsite', // This is the key fix!
          callType: serviceCall.call_billing_type || serviceCall.billing_type || serviceCall.type_of_call || '', // Load call type
          executiveId: serviceCall.assigned_to || '',
          priority: serviceCall.priority || 'medium',
          statusId: serviceCall.status_id || '',
          natureOfIssue: serviceCall.nature_of_issue_id || '',
          area: serviceCall.area_id || '',

          // Contact person
          contactPerson: serviceCall.contactPerson ?
            `${serviceCall.contactPerson.first_name || ''} ${serviceCall.contactPerson.last_name || ''}`.trim() : '',
          contactPersonId: serviceCall.contact_person_id || '',

          // Service description
          subject: serviceCall.subject || '',
          description: serviceCall.description || '',
          customerReportedIssue: serviceCall.customer_reported_issue || '',
          actualIssueFound: serviceCall.actual_issue_found || '',
          solutionProvided: serviceCall.solution_provided || '',

          // Onsite Call Fields (companyName already set above)
          onsiteExecutiveId: serviceCall.assigned_to || '',
          serviceLocation: serviceCall.service_location || '',
          locationCoordinates: serviceCall.location_coordinates || { lat: '', lng: '' },
          googleMapsLink: serviceCall.google_maps_link || '',
          tallyVersion: serviceCall.tally_version || '',
          designation: serviceCall.designation || '',
          tssStatus: serviceCall.tss_status || '',
          tssExpiry: serviceCall.tss_expiry ? serviceCall.tss_expiry.split('T')[0] : '',
          callStartTime: serviceCall.call_start_time || (serviceCall.started_at ? new Date(serviceCall.started_at).toISOString().slice(0, 16) : ''),
          callEndTime: serviceCall.call_end_time || (serviceCall.completed_at ? new Date(serviceCall.completed_at).toISOString().slice(0, 16) : ''),
          isEndTimeFixed: serviceCall.is_end_time_fixed || false,
          chargingType: serviceCall.charging_type || 'fixed',
          serviceCharges: serviceCall.service_charges || '',
          hourlyRate: serviceCall.hourly_rate || '',
          executiveRemarks: serviceCall.executive_remarks || '',
          customerFeedbackType: serviceCall.customer_feedback_type || '',
          customerFeedbackComments: serviceCall.customer_feedback_comments || '',

          // Scheduled Date
          scheduledDate: serviceCall.scheduled_date ? serviceCall.scheduled_date.split('T')[0] : new Date().toISOString().slice(0, 10),

          // Online Call Fields
          bookingDate: serviceCall.booking_date ? new Date(serviceCall.booking_date).toISOString().slice(0, 16) : '',
          onlineTypeOfCallId: serviceCall.products_issues_id || serviceCall.type_of_call_id || '',
          onlineCallTypeId: serviceCall.online_call_type_id || '',
          onlineExecutiveId: serviceCall.assigned_to || '',
          onlineStatusId: serviceCall.status_id || '',
          remarks: serviceCall.executive_remarks || serviceCall.internal_notes || '',
          startTime: serviceCall.call_start_time || (serviceCall.started_at ? new Date(serviceCall.started_at).toISOString().slice(0, 16) : ''),
          endTime: serviceCall.call_end_time || (serviceCall.completed_at ? new Date(serviceCall.completed_at).toISOString().slice(0, 16) : ''),
          mobileNumber2: serviceCall.mobile_number_2 || '',
          onlineExecutiveRemarks: serviceCall.executive_remarks || '',

          // Financial
          estimatedHours: serviceCall.estimated_hours || 0,
          actualHours: serviceCall.actual_hours || 0,
          billableHours: serviceCall.billable_hours || 0,
          totalAmount: serviceCall.total_amount || 0,

          // Additional fields
          internalNotes: serviceCall.internal_notes || '',
          customerFeedback: serviceCall.customer_feedback || '',
          customerSatisfaction: serviceCall.customer_satisfaction || 5,
          followUpRequired: serviceCall.follow_up_required || false,
          followUpDate: serviceCall.follow_up_date ? new Date(serviceCall.follow_up_date).toISOString().slice(0, 16) : '',
          followUpNotes: serviceCall.follow_up_notes || '',

          // TSS and other customer data (will be populated if customer is selected when customer is loaded)
          // Note: tssStatus and tssExpiry are already set above from service call data
          auditorName: '',
          auditorContact: '',
          autoBackupModule: false,
          whatsappGroup: false,
          tallyCloud: false,
          tallyOnMobile: false,
          tallyOnWhatsapp: false,
          cloudServiceStatus: false,
          cloudServiceExpiry: '',
        };

        console.log('🔍 Mapped service data for edit form:', {
          serviceType: mappedData.serviceType,
          callType: serviceCall.call_type,
          serviceNumber: mappedData.serviceNumber,
          customerId: mappedData.customerId
        });

        setFormData(mappedData);

        // If customer is selected, fetch additional customer details
        if (serviceCall.customer_id) {
          await handleCustomerChange(serviceCall.customer_id);
        }

        // If executive is assigned, fetch executive details for phone and email
        if (serviceCall.assigned_to) {
          await handleExecutiveChange(serviceCall.assigned_to);
        }

        toast.success('Service data loaded successfully');
      } else {
        toast.error('Service call not found');
      }
    } catch (error) {
      console.error('Error fetching service data:', error);
      toast.error('Failed to load service data');
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch customer data when customer is selected
  const handleCustomerChange = async (customerId) => {
    if (!customerId) {
      // Clear customer-related fields when no customer is selected
      setFormData(prev => ({
        ...prev,
        customerId: '',
        customerName: '',
        companyName: '',
        contactNumber: '',
        tallySerialNumber: '',
        tallyVersion: '',
        emailAddress: '',
        tssStatus: '',
        tssExpiry: '',
        designation: '',
        isLead: false,
      }));
      return;
    }

    try {
      // Show loading state
      const loadingToast = toast.loading('Fetching customer details...');

      const response = await apiService.get(`/customers/${customerId}`, {
        params: { includeRelations: true }
      });

      if (response.data?.success && response.data?.data?.customer) {
        const { customer } = response.data.data;
        const customFields = customer.custom_fields || {};
        const tssDetails = customer.tssDetails?.[0]; // Get first TSS record if available

        // Debug logging for TSS status investigation
        console.log('🔍 Customer Debug Info:', {
          customerId,
          customerName: customer.company_name,
          tssDetailsArray: customer.tssDetails,
          tssDetailsCount: customer.tssDetails?.length || 0,
          firstTssRecord: tssDetails,
          tallySerialNumber: customer.tally_serial_number
        });

        // Determine TSS status with enhanced logic - check both CustomerTSS table and custom_fields
        let tssStatus = 'inactive';
        let tssExpiry = '';

        // First check custom_fields for TSS data (primary source for some customers)
        const customFieldsTssStatus = customFields.tss_status;
        const customFieldsTssExpiry = customFields.tss_expiry_date;

        console.log('🔍 TSS Data Sources Check:', {
          customerName: customer.company_name,
          customerId: customer.id,
          customerTSSTable: tssDetails ? 'Found' : 'Not Found',
          customFieldsTssStatus,
          customFieldsTssExpiry,
          customFieldsKeys: Object.keys(customFields)
        });

        // Check custom_fields first (handles "YES"/"NO" format)
        if (customFieldsTssStatus) {
          const isActiveInCustomFields =
            customFieldsTssStatus === 'YES' ||
            customFieldsTssStatus === 'yes' ||
            customFieldsTssStatus === 'active' ||
            customFieldsTssStatus === 'Active' ||
            customFieldsTssStatus === true ||
            customFieldsTssStatus === 1;

          // Check expiry date from custom_fields
          let isNotExpired = true;
          if (customFieldsTssExpiry) {
            const expiry = new Date(customFieldsTssExpiry);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            isNotExpired = expiry >= today;
          }
          // If no expiry date is provided, assume it's still valid (isNotExpired = true)

          // Set TSS status based on the active status
          // If TSS status is "YES" but no expiry date, still show as active
          tssStatus = isActiveInCustomFields ? 'active' : 'inactive';
          tssExpiry = customFieldsTssExpiry || '';

          console.log('🔍 Custom Fields TSS Determination:', {
            customFieldsTssStatus,
            isActiveInCustomFields,
            customFieldsTssExpiry,
            isNotExpired,
            hasExpiryDate: !!customFieldsTssExpiry,
            finalStatus: tssStatus,
            finalExpiry: tssExpiry
          });
        }
        // Fallback to CustomerTSS table if custom_fields doesn't have TSS data
        else if (tssDetails) {
          const dbStatus = tssDetails.status;
          const expiryDate = tssDetails.expiry_date;

          // Check if status is explicitly active
          const isActiveInDB =
            dbStatus === 'active' ||
            dbStatus === 'Active' ||
            dbStatus === 'YES' ||
            dbStatus === 'yes' ||
            dbStatus === 1 ||
            dbStatus === true;

          // Check expiry date if present
          let isNotExpired = true;
          if (expiryDate) {
            const expiry = new Date(expiryDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            isNotExpired = expiry >= today;
          }

          tssStatus = (isActiveInDB && isNotExpired) ? 'active' : 'inactive';
          tssExpiry = expiryDate || '';

          console.log('🔍 CustomerTSS Table Determination:', {
            dbStatus,
            dbStatusType: typeof dbStatus,
            expiryDate,
            isActiveInDB,
            isNotExpired,
            finalStatus: tssStatus
          });
        }
        // No TSS data found in either source
        else {
          tssStatus = 'inactive';
          console.log('🔍 No TSS data found in custom_fields or CustomerTSS table');
        }

        // Special debug for Power Pandi (customer ID 4)
        if (customer.id === 4 || customer.company_name?.toLowerCase().includes('power pandi') ||
            customer.md_contact_person?.toLowerCase().includes('power pandi')) {
          console.log('🔍 POWER PANDI TSS DEBUG (Customer ID 4):', {
            customerId: customer.id,
            companyName: customer.company_name,
            mdContactPerson: customer.md_contact_person,
            customFields: JSON.stringify(customFields, null, 2),
            tssDetailsFromTable: tssDetails ? JSON.stringify(tssDetails, null, 2) : 'None',
            finalTssStatus: tssStatus,
            finalTssExpiry: tssExpiry
          });
        }

        // Extract auditor/tax consultant information
        const auditorName = customFields.auditor_name || customFields.tax_consultant_name || '';
        const auditorContact = customFields.auditor_no || customFields.tax_consultant_no || '';

        // Extract Tally features from custom fields - improved mapping
        const autoBackupModule = customFields.auto_backup === true ||
                                 customFields.auto_backup_enabled === true ||
                                 customFields.auto_backup_module === true;
        const whatsappGroup = customFields.whatsapp_telegram_group === true ||
                             customFields.whatsapp_group === true ||
                             customFields.telegram_group === true;
        const tallyCloud = customFields.cloud_user === true ||
                          customFields.tally_cloud === true ||
                          tssDetails?.tally_net_enabled === true;
        const tallyOnMobile = customFields.mobile_app === true ||
                             customFields.tally_on_mobile === true ||
                             customFields.mobile_access === true;
        const tallyOnWhatsapp = customFields.tally_on_whatsapp === true;

        // Extract Cloud Service information
        const cloudServiceStatus = customFields.cloud_user === true;
        const cloudServiceExpiry = customFields.cloud_user_expiry_date || '';

        console.log('🔍 Cloud Service Data Check:', {
          customerName: customer.company_name,
          customerId: customer.id,
          cloudUser: customFields.cloud_user,
          cloudUserExpiryDate: customFields.cloud_user_expiry_date,
          cloudServiceStatus,
          cloudServiceExpiry,
          isCloudActive: cloudServiceStatus && cloudServiceExpiry && new Date(cloudServiceExpiry) >= new Date()
        });

        // Special debug for Power Pandi cloud service
        if (customer.id === 4 || customer.company_name?.toLowerCase().includes('power pandi')) {
          console.log('🔍 POWER PANDI CLOUD SERVICE DEBUG:', {
            customerId: customer.id,
            companyName: customer.company_name,
            allCustomFields: Object.keys(customFields),
            cloudUser: customFields.cloud_user,
            cloudUserExpiryDate: customFields.cloud_user_expiry_date,
            cloudServiceStatus,
            cloudServiceExpiry,
            willShowCloudStatus: cloudServiceStatus,
            willShowCloudExpiry: cloudServiceStatus && cloudServiceExpiry
          });
        }

        // Determine if this is a lead (no Tally Serial Number)
        const isLead = !customer.tally_serial_number || customer.tally_serial_number.trim() === '';

        // Enhanced Tally Version mapping - check multiple possible sources
        let tallyVersionValue = '';

        // Priority order for Tally Version:
        // 1. Direct tally_version field
        // 2. TSS details version
        // 3. Custom fields tally_version or product_version
        // 4. Product information from customer data
        if (customer.tally_version) {
          tallyVersionValue = customer.tally_version;
        } else if (tssDetails?.version) {
          tallyVersionValue = tssDetails.version;
        } else if (customFields.tally_version) {
          tallyVersionValue = customFields.tally_version;
        } else if (customFields.product_version) {
          tallyVersionValue = customFields.product_version;
        } else if (customer.product_info?.version) {
          tallyVersionValue = customer.product_info.version;
        } else if (customer.products && customer.products.length > 0) {
          // Check if customer has products with version info
          const productWithVersion = customer.products.find(p => p.version);
          if (productWithVersion) {
            tallyVersionValue = productWithVersion.version;
          }
        }

        console.log('🔍 Tally Version Auto-fetch Debug:', {
          customerId,
          customerName: customer.company_name,
          directTallyVersion: customer.tally_version,
          tssDetailsVersion: tssDetails?.version,
          customFieldsTallyVersion: customFields.tally_version,
          customFieldsProductVersion: customFields.product_version,
          productInfo: customer.product_info,
          products: customer.products,
          finalTallyVersion: tallyVersionValue,
          autoFetchStatus: tallyVersionValue ? 'SUCCESS' : 'NO_DATA_FOUND'
        });

        // Determine call type based on AMC status
        const hasActiveAMC = customFields.amc_status === 'active' || customFields.amc_status === 'YES';
        const autoCallType = hasActiveAMC ? 'amc_call' : (customFields.default_call_type || 'free_call');

        setFormData(prev => ({
          ...prev,
          customerId,
          customerName: customer.company_name || customer.name || '',
          companyName: customer.company_name || '',
          contactNumber: customer.phone || '',
          tallySerialNumber: customer.tally_serial_number || '',
          tallyVersion: tallyVersionValue,
          emailAddress: customer.email || '',
          designation: customer.contact_person || customFields.office_contact_person || '',
          tssStatus,
          tssExpiry,
          isLead, // Track if this is a lead for status logic
          callType: autoCallType, // Auto-set call type based on AMC status
        }));

        // Log call type auto-selection
        console.log('🔄 Call Type Auto-selected:', {
          customerId,
          customerName: customer.company_name,
          hasActiveAMC,
          amcStatus: customFields.amc_status,
          defaultCallType: customFields.default_call_type,
          autoSelectedCallType: autoCallType
        });

        // Fetch AMC details if customer has active AMC
        if (hasActiveAMC) {
          await fetchCustomerAMCDetails(customer);
        } else {
          setSelectedCustomerAMC(null);
        }

        toast.dismiss(loadingToast);
        const callTypeMessage = hasActiveAMC ? 'AMC Call (Active AMC found)' : 'Free Call (No active AMC)';
        toast.success(`Customer details auto-filled successfully. Call type set to: ${callTypeMessage}`);
      } else {
        toast.dismiss(loadingToast);
        toast.error('Failed to fetch customer details');
      }
    } catch (error) {
      console.error('Error fetching customer data:', error);
      toast.error('Error fetching customer details');
    }
  };

  // Fetch customer AMC details
  const fetchCustomerAMCDetails = async (customer) => {
    try {
      const amcContracts = customer.amcContracts || [];

      // Find active AMC contract
      const activeAMC = amcContracts.find(amc =>
        amc.status === 'active' && amc.is_active && !amc.isExpired?.()
      );

      if (activeAMC) {
        // Calculate next visit date (assuming monthly visits)
        const nextVisitDate = new Date();
        nextVisitDate.setMonth(nextVisitDate.getMonth() + 1);

        setSelectedCustomerAMC({
          expiryDate: activeAMC.end_date,
          renewalDate: activeAMC.renewal_date,
          nextVisit: nextVisitDate.toISOString().split('T')[0],
          contractValue: activeAMC.contract_value,
          callsRemaining: activeAMC.calls_allowed ? (activeAMC.calls_allowed - activeAMC.calls_used) : null,
          visitsRemaining: activeAMC.onsite_visits_allowed ? (activeAMC.onsite_visits_allowed - activeAMC.onsite_visits_used) : null
        });
      } else {
        setSelectedCustomerAMC(null);
      }
    } catch (error) {
      console.error('Error processing customer AMC details:', error);
      setSelectedCustomerAMC(null);
    }
  };

  // Auto-fetch executive data when executive is selected
  const handleExecutiveChange = async (executiveId) => {
    if (!executiveId) {
      // Clear executive-related fields when no executive is selected
      setFormData(prev => ({
        ...prev,
        executiveId: '',
        executivePhone: '',
        executiveEmail: ''
      }));
      return;
    }

    try {
      // Find the selected executive from the loaded executives list
      const selectedExecutive = executives.find(exec => exec.id === executiveId);

      if (selectedExecutive) {
        // Auto-fill phone and email from executive data
        setFormData(prev => ({
          ...prev,
          executiveId,
          executivePhone: selectedExecutive.phone || selectedExecutive.mobile || '',
          executiveEmail: selectedExecutive.email || ''
        }));

        console.log('🔍 Executive auto-fetch:', {
          executiveId,
          executiveName: selectedExecutive.name,
          phone: selectedExecutive.phone || selectedExecutive.mobile,
          email: selectedExecutive.email
        });
      } else {
        // If executive not found in current list, fetch from API
        console.log('🔍 Executive not found in list, fetching from API...');
        const response = await apiService.get(`/executives/${executiveId}`);

        if (response.data?.success && response.data?.data?.executive) {
          const { executive } = response.data.data;
          setFormData(prev => ({
            ...prev,
            executiveId,
            executivePhone: executive.phone || executive.mobile || '',
            executiveEmail: executive.email || ''
          }));

          console.log('🔍 Executive fetched from API:', {
            executiveId,
            executiveName: executive.name || `${executive.first_name} ${executive.last_name}`,
            phone: executive.phone || executive.mobile,
            email: executive.email
          });
        }
      }
    } catch (error) {
      console.error('Error fetching executive data:', error);
      // Don't show error to user, just log it
    }
  };


  const handleInputChange = (field, value) => {
    // Handle status changes separately to avoid form submission
    if (field === 'statusId') {
      handleStatusUpdate(value);
      return;
    }

    // Handle special processing for different fields
    let processedValue = value;

    // Service number: convert to uppercase and validate format
    if (field === 'serviceNumber') {
      processedValue = value.toUpperCase();
      validateServiceNumber(processedValue);
    }

    setFormData(prev => ({ ...prev, [field]: processedValue }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Handle special cases
    if (field === 'customerId') {
      handleCustomerChange(value);
    } else if (field === 'tallySerialNumber') {
      // Tally Serial Number is read-only in create mode - skip handling

    } else if (field === 'executiveId') {
      handleExecutiveChange(value);
    }
  };

  // Separate handler for status updates to avoid form submission
  const handleStatusUpdate = async (newStatusId) => {
    if (!isEdit) {
      // For new services, just update the local state
      setFormData(prev => ({
        ...prev,
        statusId: newStatusId
      }));
      return;
    }

    try {
      setStatusUpdating(true);

      // Optimistically update the UI
      const previousStatusId = formData.statusId;
      setFormData(prev => ({
        ...prev,
        statusId: newStatusId
      }));

      // Find the status details for better feedback
      const newStatus = callStatuses.find(status => status.id === newStatusId);

      // Create the update payload with just the status
      const statusData = {
        status_id: newStatusId
      };

      console.log('🔄 Updating service status:', { id, newStatusId, statusData, statusName: newStatus?.name });

      // Make API call to update only the status
      const response = await apiService.put(`/service-calls/${id}`, statusData);

      if (response.data?.success) {
        toast.success(`Status updated to ${newStatus?.name || 'new status'}`);
        console.log('✅ Status update successful:', response.data);
      } else {
        // Revert on failure
        setFormData(prev => ({
          ...prev,
          statusId: previousStatusId
        }));
        toast.error(response.data?.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('❌ Error updating status:', error);

      // Revert on error
      setFormData(prev => ({
        ...prev,
        statusId: previousStatusId
      }));

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');
      ErrorHandler.showError(error, 'Failed to update service status');
    } finally {
      setStatusUpdating(false);
    }
  };

  // Validate service number format and uniqueness
  const validateServiceNumber = async (serviceNumber) => {
    if (!serviceNumber) return;

    // Basic validation: must contain at least one letter or number, no spaces
    const hasValidChars = /^[A-Z0-9\-_]+$/.test(serviceNumber);
    const hasContent = serviceNumber.length >= 1;
    const noConsecutiveHyphens = !serviceNumber.includes('--') && !serviceNumber.includes('__');
    const noStartEndHyphen = !serviceNumber.startsWith('-') && !serviceNumber.endsWith('-') &&
                             !serviceNumber.startsWith('_') && !serviceNumber.endsWith('_');

    if (!hasValidChars || !hasContent || !noConsecutiveHyphens || !noStartEndHyphen) {
      setErrors(prev => ({
        ...prev,
        serviceNumber: 'Service number can contain letters, numbers, hyphens, and underscores. No spaces or special characters allowed.'
      }));
      return;
    }

    // Clear format error if validation passes
    setErrors(prev => ({
      ...prev,
      serviceNumber: null
    }));

    // Check uniqueness (debounced to avoid too many API calls)
    clearTimeout(window.serviceNumberValidationTimeout);
    window.serviceNumberValidationTimeout = setTimeout(async () => {
      try {
        const response = await apiService.get(`/service-calls?search=${serviceNumber}&limit=1`);
        const existingCall = response.data?.data?.serviceCalls?.find(
          call => call.call_number === serviceNumber
        );

        if (existingCall && (!isEdit || existingCall.id !== id)) {
          setErrors(prev => ({
            ...prev,
            serviceNumber: 'This service number already exists. Please use a different number.'
          }));
        } else {
          // Only clear uniqueness error, keep format error if it exists
          setErrors(prev => {
            const newErrors = { ...prev };
            if (newErrors.serviceNumber && newErrors.serviceNumber.includes('already exists')) {
              newErrors.serviceNumber = null;
            }
            return newErrors;
          });
        }
      } catch (error) {
        console.error('Error validating service number:', error);
      }
    }, 500);
  };

  const handleLocationSelect = (location) => {
    setFormData(prev => ({
      ...prev,
      serviceLocation: location.address,
      locationCoordinates: { lat: location.lat, lng: location.lng },
      googleMapsLink: `https://maps.google.com/?q=${location.lat},${location.lng}`
    }));
    setShowMapPicker(false);
    toast.success('Location selected successfully');
  };

  // Handle new customer creation
  const handleNewCustomerClick = () => {
    setShowNewCustomerModal(true);
  };

  const handleCustomerCreated = (newCustomer) => {
    // Add the new customer to the customers list
    setCustomers(prev => [newCustomer, ...prev]);

    // Auto-select the new customer in the form
    setFormData(prev => ({
      ...prev,
      customerId: newCustomer.id,
      customerName: newCustomer.company_name || newCustomer.name,
      companyName: newCustomer.company_name,
      contactNumber: newCustomer.phone,
      tallySerialNumber: newCustomer.tally_serial_number || '',
      emailAddress: newCustomer.email || '',
      isLead: !newCustomer.tally_serial_number || newCustomer.tally_serial_number.trim() === '',
      // Populate additional fields from custom_fields if available
      designation: newCustomer.custom_fields?.office_contact_person || '',
      tssStatus: newCustomer.custom_fields?.tss_status || '',
      tssExpiry: newCustomer.custom_fields?.tss_expiry || '',
    }));

    toast.success('Customer created and selected successfully');
  };

  const handleCreateNewFromSearch = (searchTerm) => {
    // Pre-fill the customer name in the modal
    setShowNewCustomerModal(true);
    // You could pass searchTerm to the modal if needed
    toast.info(`Creating new customer: ${searchTerm}`);
  };

  const calculateTotalHours = () => {
    if (!formData.callStartTime || !formData.callEndTime) return 0;

    const start = new Date(formData.callStartTime);
    const end = new Date(formData.callEndTime);
    const diffMs = end - start;
    const diffHours = diffMs / (1000 * 60 * 60);

    return Math.max(0, Math.round(diffHours * 100) / 100);
  };

  const calculateServiceAmount = () => {
    if (formData.chargingType === 'hourly') {
      const hours = calculateTotalHours();
      const rate = parseFloat(formData.hourlyRate) || 0;
      return hours * rate;
    }
    return parseFloat(formData.serviceCharges) || 0;
  };

  const validateForm = () => {
    const newErrors = {};

    // Mandatory fields validation
    if (!formData.serviceNumber) {
      newErrors.serviceNumber = 'Service number is required';
    } else {
      // Use the same flexible validation as the real-time validator
      const hasValidChars = /^[A-Z0-9\-_]+$/.test(formData.serviceNumber);
      const hasContent = formData.serviceNumber.length >= 1;
      const noConsecutiveHyphens = !formData.serviceNumber.includes('--') && !formData.serviceNumber.includes('__');
      const noStartEndHyphen = !formData.serviceNumber.startsWith('-') && !formData.serviceNumber.endsWith('-') &&
                               !formData.serviceNumber.startsWith('_') && !formData.serviceNumber.endsWith('_');

      if (!hasValidChars || !hasContent || !noConsecutiveHyphens || !noStartEndHyphen) {
        newErrors.serviceNumber = 'Service number can contain letters, numbers, hyphens, and underscores. No spaces or special characters allowed.';
      }
    }

    if (!formData.customerId) newErrors.customerId = 'Customer name is required';
    if (!formData.contactNumber) {
      newErrors.contactNumber = 'Contact number is required';
    } else {
      const phoneValidation = validationRules.phone(formData.contactNumber, true);
      if (!phoneValidation.isValid) {
        newErrors.contactNumber = phoneValidation.message;
      }
    }

    // Optional fields don't need validation
    // All other fields are optional as per requirements

    console.log('🔍 Form validation results:', {
      formData: {
        serviceNumber: formData.serviceNumber,
        customerId: formData.customerId,
        contactNumber: formData.contactNumber,
        isLead: formData.isLead
      },
      errors: newErrors,
      isValid: Object.keys(newErrors).length === 0
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Auto-generate service number if empty
    if (!formData.serviceNumber) {
      await generateServiceNumber();
      // Wait a moment for state to update
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    try {
      setSaving(true);

      // Determine status to use: user selection or default based on customer type
      let statusToUse = formData.statusId; // Use user's selection if available

      if (!statusToUse) {
        // Only use default if user hasn't selected a status
        if (formData.isLead || !formData.tallySerialNumber) {
          // For leads (no Tally Serial Number): Default status = "Follow Up Customer"
          const followUpStatus = callStatuses.find(status =>
            status.code === 'FOLLOW_UP_CUSTOMER' ||
            status.name.toLowerCase().includes('follow up customer')
          );
          statusToUse = followUpStatus?.id;
        } else {
          // For existing customers (has Tally Serial Number): Default status = "Open"
          const openStatus = callStatuses.find(status =>
            status.code === 'OPEN' ||
            status.name.toLowerCase() === 'open'
          );
          statusToUse = openStatus?.id;
        }
      }

      // Debug logging for status submission
      console.log('🔍 Status Debug Info:', {
        userSelectedStatusId: formData.statusId,
        statusToUse,
        selectedStatusName: callStatuses.find(s => s.id === statusToUse)?.name,
        selectedStatusCode: callStatuses.find(s => s.id === statusToUse)?.code,
        isEdit,
        allAvailableStatuses: callStatuses.map(s => ({ id: s.id, name: s.name, code: s.code }))
      });

      const submitData = {
        call_number: formData.serviceNumber,
        customer_id: formData.customerId,
        contact_number: formData.contactNumber,
        company_name: formData.companyName,
        tally_serial_number: formData.tallySerialNumber,
        service_location: formData.serviceLocation,
        service_charges: formData.serviceCharges ? parseFloat(formData.serviceCharges) : null,
        assigned_to: formData.executiveId || null,
        products_issues_id: formData.typeOfCallId || null, // Map to correct backend field (products_issues table)
        call_billing_type: formData.callType || '', // Add call type field - empty string will trigger backend default
        status_id: statusToUse,
        // Additional customer information will be auto-added in background
        designation: formData.designation,
        tally_version: formData.tallyVersion,
        tss_status: formData.tssStatus,
        tss_expiry: formData.tssExpiry && formData.tssExpiry !== 'Invalid date' && formData.tssExpiry.trim() !== '' ? formData.tssExpiry : null,
        email_address: formData.emailAddress,
        // Executive contact information (auto-fetched but editable)
        executive_phone: formData.executivePhone || null,
        executive_email: formData.executiveEmail || null,
        // Map location coordinates if available
        location_coordinates: formData.locationCoordinates,
        google_maps_link: formData.googleMapsLink,
        // Set default values for required fields
        subject: `Service Call - ${formData.customerName}`,
        description: `Service call for ${formData.companyName || formData.customerName}${formData.typeOfCallId ? ' - Issue type selected' : ''}`,
        call_type: 'onsite', // Default call type
        priority: 'medium', // Default priority
      };

      // Debug logging for API submission
      console.log('🚀 Submitting service call data:', {
        isEdit,
        serviceCallId: id,
        submitData,
        statusInfo: {
          statusId: submitData.status_id,
          statusName: callStatuses.find(s => s.id === submitData.status_id)?.name,
          statusCode: callStatuses.find(s => s.id === submitData.status_id)?.code
        }
      });

      let response;
      if (isEdit) {
        response = await apiService.put(`/service-calls/${id}`, submitData);
      } else {
        response = await apiService.post('/service-calls', submitData);
      }

      // Debug logging for API response
      console.log('📥 API Response:', {
        success: response.data?.success,
        message: response.data?.message,
        data: response.data?.data
      });

      if (response.data?.success) {
        const selectedStatus = callStatuses.find(s => s.id === submitData.status_id);
        const statusCode = selectedStatus?.code;

        // Timer Integration: Backend automatically handles timer start/stop based on status
        // - "In Progress" status codes (ON_PROCESS, PROGRESS, IN_PROGRESS) automatically start timer
        // - "Completed", "Cancelled", "Follow Up" statuses automatically stop timer
        // - No frontend action needed - all timer logic is backend-managed
        if (statusCode && ['ON_PROCESS', 'PROGRESS', 'IN_PROGRESS'].includes(statusCode)) {
          console.log('🔄 Timer will be automatically started by backend for status:', selectedStatus.name);
        }

        toast.success(`Service ${isEdit ? 'updated' : 'created'} successfully`);

        // Show feedback modal if status is completed
        if (formData.statusId && callStatuses.find(s => s.id === formData.statusId)?.name === 'Completed') {
          setShowFeedbackModal(true);
        } else {
          navigate('/services');
        }
      }
    } catch (error) {
      console.error('Error saving service:', error);
      toast.error(`Failed to ${isEdit ? 'update' : 'create'} service`);
    } finally {
      setSaving(false);
    }
  };

  const handleFeedbackSubmit = (feedbackData) => {
    setFormData(prev => ({
      ...prev,
      customerFeedbackType: feedbackData.type,
      customerFeedbackComments: feedbackData.comments
    }));
    setShowFeedbackModal(false);
    navigate('/services');
  };

  // Debug function to test specific customer TSS status
  const debugCustomerTSS = async (customerId) => {
    try {
      console.log('🔍 Testing customer TSS status for ID:', customerId);
      const response = await apiService.get(`/customers/${customerId}`, {
        params: { includeRelations: true }
      });

      if (response.data?.success) {
        const { customer } = response.data.data;
        console.log('🔍 Customer TSS Debug:', {
          customerId,
          customerName: customer.company_name,
          tssDetails: customer.tssDetails,
          rawTssData: JSON.stringify(customer.tssDetails, null, 2)
        });
      }
    } catch (error) {
      console.error('🔍 Customer TSS Debug Error:', error);
    }
  };

  // Debug function specifically for Power Pandi (Customer ID 4)
  const debugPowerPandiCustomer = async () => {
    console.log('🔍 Debugging Power Pandi Customer (ID: 4)...');
    try {
      const response = await apiService.get('/customers/4', {
        params: { includeRelations: true }
      });

      if (response.data?.success) {
        const { customer } = response.data.data;
        console.log('🔍 Power Pandi Raw Data:', {
          id: customer.id,
          company_name: customer.company_name,
          md_contact_person: customer.md_contact_person,
          custom_fields: customer.custom_fields,
          tssDetails: customer.tssDetails,
          rawCustomerData: JSON.stringify(customer, null, 2)
        });

        // Test TSS determination logic manually
        const customFields = customer.custom_fields || {};
        const tssStatus = customFields.tss_status;
        const tssExpiry = customFields.tss_expiry_date;

        console.log('🔍 Manual TSS Logic Test:', {
          tssStatus,
          tssExpiry,
          isYES: tssStatus === 'YES',
          isActive: tssStatus === 'YES' && new Date(tssExpiry) >= new Date()
        });

        return customer;
      }
    } catch (error) {
      console.error('🔍 Error debugging Power Pandi:', error);
    }
  };

  // Test customer data fetch manually for debugging any customer
  const testCustomerAutoFetch = async (customerId) => {
    try {
      console.log('🧪 Testing auto-fetch functionality for customer ID:', customerId);

      const response = await apiService.get(`/customers/${customerId}`, {
        params: { includeRelations: true }
      });

      if (response.data?.success && response.data?.data?.customer) {
        const { customer } = response.data.data;
        const customFields = customer.custom_fields || {};
        const tssDetails = customer.tssDetails?.[0];

        // Test Tally Version auto-fetch logic
        const tallyVersionSources = {
          directTallyVersion: customer.tally_version,
          tssDetailsVersion: tssDetails?.version,
          customFieldsTallyVersion: customFields.tally_version,
          customFieldsProductVersion: customFields.product_version,
          productInfo: customer.product_info?.version,
          productsArray: customer.products?.find(p => p.version)?.version
        };

        const finalTallyVersion = tallyVersionSources.directTallyVersion ||
                                 tallyVersionSources.tssDetailsVersion ||
                                 tallyVersionSources.customFieldsTallyVersion ||
                                 tallyVersionSources.customFieldsProductVersion ||
                                 tallyVersionSources.productInfo ||
                                 tallyVersionSources.productsArray || '';

        // Test TSS Status auto-fetch logic
        const tssStatusSources = {
          customFieldsTssStatus: customFields.tss_status,
          customFieldsTssExpiry: customFields.tss_expiry_date,
          tssTableStatus: tssDetails?.status,
          tssTableExpiry: tssDetails?.expiry_date
        };

        const isActiveFromCustomFields = ['YES', 'yes', 'active', 'Active', true, 1].includes(customFields.tss_status);
        const isActiveFromTssTable = ['active', 'Active', 'YES', 'yes', true, 1].includes(tssDetails?.status);

        console.log('🧪 Auto-Fetch Test Results:', {
          customerId,
          customerName: customer.company_name,
          tallyVersionTest: {
            sources: tallyVersionSources,
            finalValue: finalTallyVersion,
            willAutoFetch: !!finalTallyVersion
          },
          tssStatusTest: {
            sources: tssStatusSources,
            isActiveFromCustomFields,
            isActiveFromTssTable,
            finalStatus: (customFields.tss_status ? isActiveFromCustomFields : isActiveFromTssTable) ? 'active' : 'inactive',
            willAutoFetch: !!(customFields.tss_status || tssDetails?.status)
          },
          overallAutoFetchStatus: {
            tallyVersionAvailable: !!finalTallyVersion,
            tssStatusAvailable: !!(customFields.tss_status || tssDetails?.status),
            bothAvailable: !!finalTallyVersion && !!(customFields.tss_status || tssDetails?.status)
          }
        });

        return {
          customer,
          autoFetchResults: {
            tallyVersion: finalTallyVersion,
            tssStatus: (customFields.tss_status ? isActiveFromCustomFields : isActiveFromTssTable) ? 'active' : 'inactive'
          }
        };
      }
    } catch (error) {
      console.error('🧪 Auto-fetch test failed:', error);
    }
  };

  // Debug function to test timer creation
  const testTimerCreation = async () => {
    try {
      console.log('🧪 Testing timer creation with In Progress status...');

      // Find an "In Progress" status
      const inProgressStatus = callStatuses.find(status =>
        ['ON_PROCESS', 'PROGRESS', 'IN_PROGRESS'].includes(status.code)
      );

      if (!inProgressStatus) {
        console.error('❌ No In Progress status found');
        return;
      }

      console.log('✅ Found In Progress status:', {
        id: inProgressStatus.id,
        name: inProgressStatus.name,
        code: inProgressStatus.code
      });

      // Set form data to create a test service call
      const testData = {
        serviceNumber: `TEST-TIMER-${Date.now()}`,
        customerId: customers[0]?.id, // Use first customer
        contactNumber: customers[0]?.phone || '1234567890',
        companyName: customers[0]?.company_name || 'Test Company',
        statusId: inProgressStatus.id, // Set to In Progress
        subject: 'Timer Creation Test',
        description: 'Testing timer functionality during creation'
      };

      console.log('🔄 Creating test service call with data:', testData);

      // Submit the test service call
      const response = await apiService.post('/service-calls', {
        call_number: testData.serviceNumber,
        customer_id: testData.customerId,
        contact_number: testData.contactNumber,
        company_name: testData.companyName,
        status_id: testData.statusId,
        subject: testData.subject,
        description: testData.description,
        call_type: 'onsite',
        priority: 'medium'
      });

      if (response.data?.success) {
        const serviceCallId = response.data.data.serviceCall.id;
        console.log('✅ Test service call created:', serviceCallId);

        // Check timer status
        setTimeout(async () => {
          try {
            const timerResponse = await apiService.get(`/debug/timer-status/${serviceCallId}`);
            console.log('📊 Timer status check:', timerResponse.data);

            if (timerResponse.data?.success) {
              const { debug, timeHistory } = timerResponse.data.data;

              if (debug.hasStartedAt && debug.hasTimeHistory) {
                console.log('✅ SUCCESS: Timer started correctly!');
                console.log('Timer details:', {
                  timeHistoryLength: timeHistory.length,
                  lastAction: debug.lastAction
                });
              } else {
                console.log('❌ FAILURE: Timer did not start');
                console.log('Missing:', {
                  startedAt: !debug.hasStartedAt,
                  timeHistory: !debug.hasTimeHistory
                });
              }
            }

            // Clean up test data
            await apiService.delete(`/service-calls/${serviceCallId}`);
            console.log('🧹 Test service call deleted');

          } catch (error) {
            console.error('❌ Timer status check failed:', error);
          }
        }, 1000);

      } else {
        console.error('❌ Failed to create test service call:', response.data);
      }

    } catch (error) {
      console.error('❌ Timer creation test failed:', error);
    }
  };

  // Add functions to window for easy testing in console
  if (typeof window !== 'undefined') {
    window.debugCustomerTSS = debugCustomerTSS;
    window.debugPowerPandiCustomer = debugPowerPandiCustomer;
    window.testCustomerID4 = () => handleCustomerChange(4);
    window.testTimerCreation = testTimerCreation;
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 theme-card-bg rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">
          {isEdit ? 'Edit Service Call' : 'Create New Service Call'}
        </h1>
        <button
          onClick={() => navigate('/services')}
          className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
        >
          <FaTimes className="inline mr-2" />
          Cancel
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Validation Error Summary */}
        {Object.keys(errors).length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Please fix the following validation errors:
                </h3>
              </div>
            </div>
            <div className="ml-8">
              <ul className="list-disc list-inside text-sm text-red-700 space-y-1">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>
                    <strong>{getFieldLabel(field)}:</strong> {error}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Add New Customer Button - Only show in create mode */}
        {!isEdit && (
          <div className="mb-6">
            <button
              type="button"
              onClick={handleNewCustomerClick}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <FaPlus className="h-4 w-4" />
              <span>Add New Customer</span>
            </button>
          </div>
        )}

        {/* Conditional Form Rendering */}
        {isEdit ? (
          /* EDIT MODE: Show comprehensive form with all sections */
          <>
            {/* Section 1: Service Information */}
            <div className="bg-blue-50 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold text-blue-800 mb-4 flex items-center">
                <FaTools className="mr-2" />
                Service Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Service Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Service Number *
                  </label>
                  <input
                    type="text"
                    value={formData.serviceNumber}
                    onChange={(e) => handleInputChange('serviceNumber', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  {errors.serviceNumber && (
                    <p className="text-red-500 text-xs mt-1">{errors.serviceNumber}</p>
                  )}
                </div>

                {/* Service Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Service Type
                  </label>
                  <select
                    value={formData.serviceType || 'onsite'}
                    onChange={(e) => handleInputChange('serviceType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="onsite">Onsite</option>
                    <option value="online">Online</option>
                  </select>
                </div>

                {/* Priority */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <select
                    value={formData.priority || 'medium'}
                    onChange={(e) => handleInputChange('priority', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>


                {/* Type of Call */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type of Call
                  </label>
                  <SearchableSelect
                    options={productsIssues.map(item => ({
                      id: item.id,
                      name: item.name,
                      category: item.category,
                      description: item.description,
                      displayName: item.category ? `${item.category} - ${item.name}` : item.name
                    }))}
                    value={formData.typeOfCallId}
                    onChange={(value) => handleInputChange('typeOfCallId', value)}
                    placeholder="Support, Installation, etc..."
                    searchFields={['name', 'category', 'description']}
                    displayField="displayName"
                    valueField="id"
                    error={!!errors.typeOfCallId}
                    groupBy="category"
                    minSearchLength={1}
                  />
                  {errors.typeOfCallId && <p className="text-red-500 text-xs mt-1">{errors.typeOfCallId}</p>}
                </div>

                {/* Call Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Call Type
                  </label>
                  <select
                    value={formData.callType || ''}
                    onChange={(e) => handleInputChange('callType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select call type (optional)</option>
                    <option value="free_call">Free Call</option>
                    <option value="amc_call">AMC Call</option>
                    <option value="per_call">Per Call</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">Optional - specify the call type for this service call</p>
                </div>



                {/* Call Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Call Date
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.callStartTime}
                    onChange={(e) => handleInputChange('callStartTime', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                    {statusUpdating && (
                      <span className="ml-2 text-xs text-blue-600">
                        <span className="animate-spin inline-block w-3 h-3 border border-blue-600 border-t-transparent rounded-full mr-1"></span>
                        Updating...
                      </span>
                    )}
                  </label>
                  <select
                    value={formData.statusId || ''}
                    onChange={(e) => handleInputChange('statusId', e.target.value)}
                    disabled={statusUpdating}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.statusId ? 'border-red-500' : 'border-gray-300'
                    } ${statusUpdating ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  >
                    <option value="">Select status...</option>
                    {callStatuses.map((status) => (
                      <option key={status.id} value={status.id}>
                        {status.name} {status.code && `(${status.code})`}
                      </option>
                    ))}
                  </select>
                  {errors.statusId && <p className="text-red-500 text-xs mt-1">{errors.statusId}</p>}
                  {statusUpdating && (
                    <p className="text-blue-600 text-xs mt-1">
                      🔄 Updating status...
                    </p>
                  )}
                  {!statusUpdating && (
                    <p className="text-xs text-gray-500 mt-1">
                      Change status to control timer. "In Progress" starts timer automatically.
                    </p>
                  )}
                </div>
              </div>

              {/* Subject and Description */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject
                  </label>
                  <input
                    type="text"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    placeholder="Service Call Subject"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Service call description"
                    rows="3"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Hours Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Hours
                  </label>
                  <input
                    type="number"
                    step="0.25"
                    value={formData.estimatedHours}
                    onChange={(e) => handleInputChange('estimatedHours', e.target.value)}
                    placeholder="0.00"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Actual Hours
                  </label>
                  <input
                    type="number"
                    step="0.25"
                    value={formData.actualHours}
                    onChange={(e) => handleInputChange('actualHours', e.target.value)}
                    placeholder="0.00"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Billable Hours
                  </label>
                  <input
                    type="number"
                    step="0.25"
                    value={formData.billableHours}
                    onChange={(e) => handleInputChange('billableHours', e.target.value)}
                    placeholder="0.00"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Section 2: Customer & Contact Information */}
            <div className="bg-green-50 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold text-green-800 mb-4 flex items-center">
                <FaUser className="mr-2" />
                Customer & Contact Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Customer Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer Name *
                  </label>
                  <SearchableSelect
                    options={customers}
                    value={formData.customerId}
                    onChange={(value) => handleInputChange('customerId', value)}
                    placeholder="Search customers..."
                    searchFields={['company_name', 'contact_person', 'display_name', 'phone', 'customer_code', 'tally_serial_number']}
                    displayField="company_name"
                    valueField="id"
                    error={!!errors.customerId}
                    minSearchLength={2}
                    // Server-side search props
                    onSearch={searchCustomers}
                    isSearching={isSearching}
                    searchResults={searchResults}
                    onSearchReset={resetSearch}
                    renderOption={(customer, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer ${isHighlighted ? 'bg-blue-50' : 'hover:bg-gray-50'}`}>
                        <div className="font-medium text-gray-900">{customer.company_name || customer.display_name}</div>
                        <div className="text-sm text-gray-500">{customer.contact_person || 'No contact person'}</div>
                        <div className="text-xs text-gray-400 flex items-center justify-between">
                          <span>{customer.phone || 'No phone'}</span>
                          <span className="bg-gray-200 px-2 py-1 rounded text-xs">{customer.customer_code}</span>
                        </div>
                      </div>
                    )}
                    renderSelected={(customer) => (
                      <div className="flex items-center">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{customer.company_name || customer.name}</div>
                          <div className="text-xs text-gray-500">{customer.customer_code}</div>
                        </div>
                      </div>
                    )}
                  />
                  {errors.customerId && <p className="text-red-500 text-xs mt-1">{errors.customerId}</p>}
                </div>

                {/* Company Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name
                  </label>
                  <input
                    type="text"
                    value={formData.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                    placeholder="Company name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Contact Person */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Person
                  </label>
                  <input
                    type="text"
                    value={formData.contactPerson}
                    onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                    placeholder="Contact person name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone *
                  </label>
                  <MobileInput
                    value={formData.contactNumber}
                    onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                    placeholder="Contact number"
                    error={!!errors.contactNumber}
                    className="w-full"
                  />
                  {errors.contactNumber && <p className="text-red-500 text-xs mt-1">{errors.contactNumber}</p>}
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={formData.emailAddress}
                    onChange={(e) => handleInputChange('emailAddress', e.target.value)}
                    placeholder="Email address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Service Location */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Service Location
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={formData.serviceLocation}
                      onChange={(e) => handleInputChange('serviceLocation', e.target.value)}
                      placeholder="Service address"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      type="button"
                      onClick={() => setShowMapPicker(true)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <FaMapMarkerAlt />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* AMC Details - Show when customer with active AMC is selected */}
            {selectedCustomerAMC && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-semibold text-blue-800 mb-4 flex items-center">
                  <FaCheckCircle className="mr-2" />
                  AMC Contract Details
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-600 mb-1">AMC Expiry Date</label>
                    <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                      {selectedCustomerAMC.expiryDate ? new Date(selectedCustomerAMC.expiryDate).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-blue-600 mb-1">Renewal Date</label>
                    <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                      {selectedCustomerAMC.renewalDate ? new Date(selectedCustomerAMC.renewalDate).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-blue-600 mb-1">Next Visit</label>
                    <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                      {selectedCustomerAMC.nextVisit ? new Date(selectedCustomerAMC.nextVisit).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                  {selectedCustomerAMC.callsRemaining !== null && (
                    <div>
                      <label className="block text-sm font-medium text-blue-600 mb-1">Calls Remaining</label>
                      <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                        {selectedCustomerAMC.callsRemaining}
                      </div>
                    </div>
                  )}
                  {selectedCustomerAMC.visitsRemaining !== null && (
                    <div>
                      <label className="block text-sm font-medium text-blue-600 mb-1">Visits Remaining</label>
                      <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                        {selectedCustomerAMC.visitsRemaining}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Section 3: Assigned Executive */}
            <div className="bg-purple-50 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold text-purple-800 mb-4 flex items-center">
                <FaUser className="mr-2" />
                Assigned Executive
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Executive Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Technician
                  </label>
                  <SearchableSelect
                    options={executives}
                    value={formData.executiveId}
                    onChange={(value) => handleInputChange('executiveId', value)}
                    placeholder="Search executives..."
                    searchFields={['first_name', 'last_name', 'name', 'employee_code']}
                    displayField="name"
                    valueField="id"
                    error={!!errors.executiveId}
                    minSearchLength={1}
                    renderOption={(executive, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer ${isHighlighted ? 'bg-blue-50' : 'hover:bg-gray-50'}`}>
                        <div className="font-medium text-gray-900">
                          {executive.name || `${executive.first_name || ''} ${executive.last_name || ''}`.trim()}
                        </div>
                        <div className="text-sm text-gray-500">
                          {executive.employee_code} • {executive.department || 'No Department'}
                        </div>
                      </div>
                    )}
                  />
                  {errors.executiveId && <p className="text-red-500 text-xs mt-1">{errors.executiveId}</p>}
                </div>

                {/* Executive Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone
                  </label>
                  <div className="relative">
                    <MobileInput
                      value={formData.executivePhone || ''}
                      onChange={(e) => handleInputChange('executivePhone', e.target.value)}
                      placeholder="Auto-filled from executive data"
                      className="w-full"
                    />
                    {formData.executivePhone && formData.executiveId && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <span className="text-green-500 text-sm" title="Auto-fetched from executive">✅</span>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Auto-fetched when executive is selected, editable</p>
                </div>

                {/* Executive Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      value={formData.executiveEmail || ''}
                      onChange={(e) => handleInputChange('executiveEmail', e.target.value)}
                      placeholder="Auto-filled from executive data"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    {formData.executiveEmail && formData.executiveId && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <span className="text-green-500 text-sm" title="Auto-fetched from executive">✅</span>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Auto-fetched when executive is selected, editable</p>
                </div>
              </div>
            </div>

            {/* Section 4: Tally & Technical Details */}
            <div className="bg-yellow-50 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold text-yellow-800 mb-4 flex items-center">
                <FaTools className="mr-2" />
                Tally & Technical Details
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Tally Serial Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tally Serial Number
                  </label>
                  <input
                    type="text"
                    value={formData.tallySerialNumber}
                    onChange={(e) => handleInputChange('tallySerialNumber', e.target.value)}
                    placeholder="Tally serial number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Tally Version */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tally Version
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.tallyVersion}
                      onChange={(e) => handleInputChange('tallyVersion', e.target.value)}
                      placeholder="Auto-fetched from customer data"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    {formData.tallyVersion && formData.customerId && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <span className="text-green-500 text-sm" title="Auto-fetched from customer data">✅</span>
                      </div>
                    )}
                    {!formData.tallyVersion && formData.customerId && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <span className="text-yellow-500 text-sm" title="No Tally version found in customer data">⚠️</span>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Auto-fetched when customer is selected, editable</p>
                </div>

                {/* Designation */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Designation
                  </label>
                  <input
                    type="text"
                    value={formData.designation}
                    onChange={(e) => handleInputChange('designation', e.target.value)}
                    placeholder="Customer designation"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* TSS Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    TSS Status
                  </label>
                  <div className="flex items-center space-x-2">
                    <select
                      value={formData.tssStatus}
                      onChange={(e) => handleInputChange('tssStatus', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select TSS Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                    {formData.tssStatus === 'active' && (
                      <span className="text-green-500 text-sm">✅</span>
                    )}
                    {formData.tssStatus === 'inactive' && (
                      <span className="text-red-500 text-sm">❌</span>
                    )}
                  </div>
                </div>

                {/* TSS Expiry */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    TSS Expiry
                  </label>
                  <input
                    type="date"
                    value={formData.tssExpiry}
                    onChange={(e) => handleInputChange('tssExpiry', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Section 5: Financial Information */}
            <div className="bg-red-50 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold text-red-800 mb-4 flex items-center">
                <FaRupeeSign className="mr-2" />
                Financial Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Service Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Service Amount (₹)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.serviceCharges}
                    onChange={(e) => handleInputChange('serviceCharges', e.target.value)}
                    placeholder="1000"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Currency */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency
                  </label>
                  <select
                    value={formData.currency || 'INR'}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="INR">INR</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                  </select>
                </div>

                {/* Charging Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Charging Type
                  </label>
                  <select
                    value={formData.chargingType || 'fixed'}
                    onChange={(e) => handleInputChange('chargingType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="fixed">Fixed</option>
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                  </select>
                </div>

                {/* Payment Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Payment Status
                  </label>
                  <select
                    value={formData.paymentStatus || 'pending'}
                    onChange={(e) => handleInputChange('paymentStatus', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="partial">Partial</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                {/* Hourly Rate (if charging type is hourly) */}
                {formData.chargingType === 'hourly' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hourly Rate (₹)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.hourlyRate}
                      onChange={(e) => handleInputChange('hourlyRate', e.target.value)}
                      placeholder="500"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                )}

                {/* Total Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Total Amount (₹)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.totalAmount}
                    onChange={(e) => handleInputChange('totalAmount', e.target.value)}
                    placeholder="Calculated automatically"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Section 6: Additional Information */}
            <div className="bg-gray-50 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <FaComments className="mr-2" />
                Additional Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Executive Remarks */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Executive Remarks
                  </label>
                  <textarea
                    value={formData.executiveRemarks}
                    onChange={(e) => handleInputChange('executiveRemarks', e.target.value)}
                    placeholder="Executive notes and remarks"
                    rows="4"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Internal Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Internal Notes
                  </label>
                  <textarea
                    value={formData.internalNotes}
                    onChange={(e) => handleInputChange('internalNotes', e.target.value)}
                    placeholder="Internal notes (not visible to customer)"
                    rows="4"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </>
        ) : (
          /* CREATE MODE: Show simplified form */
          <div className="bg-blue-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-blue-800 mb-4 flex items-center">
              <FaUser className="mr-2" />
              Service Call Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Service Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Service Number *
                </label>
                <input
                  type="text"
                  value={formData.serviceNumber}
                  onChange={(e) => handleInputChange('serviceNumber', e.target.value)}
                  placeholder="Enter service number (e.g., SER-001, SE-003S, TSS_123) or leave empty for auto-generation"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.serviceNumber
                      ? 'border-red-300 bg-red-50'
                      : 'border-gray-300 bg-white hover:border-blue-300'
                  }`}
                />
                {errors.serviceNumber && (
                  <p className="text-red-500 text-xs mt-1">{errors.serviceNumber}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Auto-generated if left empty.
                </p>
              </div>

              {/* Customer Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Name *
                </label>
                <SearchableSelect
                  options={customers}
                  value={formData.customerId}
                  onChange={(value) => handleInputChange('customerId', value)}
                  placeholder="Search customers..."
                  searchFields={['company_name', 'contact_person', 'display_name', 'phone', 'customer_code', 'tally_serial_number']}
                  displayField="company_name"
                  valueField="id"
                  error={!!errors.customerId}
                  minSearchLength={2}
                  showCreateOption={true}
                  onCreateNew={handleCreateNewFromSearch}
                  // Server-side search props
                  onSearch={searchCustomers}
                  isSearching={isSearching}
                  searchResults={searchResults}
                  onSearchReset={resetSearch}
                  createNewText="Create new customer"
                  noResultsText="Customer not found. Would you like to create a new customer?"
                  renderOption={(customer, isHighlighted) => (
                    <div className={`px-4 py-3 cursor-pointer ${isHighlighted ? 'bg-blue-50' : 'hover:bg-gray-50'}`}>
                      <div className="font-medium text-gray-900">{customer.company_name || customer.display_name}</div>
                      <div className="text-sm text-gray-500">{customer.contact_person || 'No contact person'}</div>
                      <div className="text-xs text-gray-400 flex items-center justify-between">
                        <span>{customer.phone || 'No phone'}</span>
                        <span className="bg-gray-200 px-2 py-1 rounded text-xs">{customer.customer_code}</span>
                      </div>
                    </div>
                  )}
                  renderSelected={(customer) => (
                    <div className="flex items-center">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{customer.company_name || customer.name}</div>
                        <div className="text-xs text-gray-500">{customer.customer_code}</div>
                      </div>
                    </div>
                  )}
                />
                {errors.customerId && <p className="text-red-500 text-xs mt-1">{errors.customerId}</p>}
              </div>

              {/* AMC Details - Show when customer with active AMC is selected */}
              {selectedCustomerAMC && (
                <div className="col-span-full bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-700 mb-3 flex items-center">
                    <FaCheckCircle className="mr-2" />
                    AMC Contract Details
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-blue-600 mb-1">AMC Expiry Date</label>
                      <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                        {selectedCustomerAMC.expiryDate ? new Date(selectedCustomerAMC.expiryDate).toLocaleDateString() : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-blue-600 mb-1">Renewal Date</label>
                      <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                        {selectedCustomerAMC.renewalDate ? new Date(selectedCustomerAMC.renewalDate).toLocaleDateString() : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-blue-600 mb-1">Next Visit</label>
                      <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                        {selectedCustomerAMC.nextVisit ? new Date(selectedCustomerAMC.nextVisit).toLocaleDateString() : 'N/A'}
                      </div>
                    </div>
                    {selectedCustomerAMC.callsRemaining !== null && (
                      <div>
                        <label className="block text-xs font-medium text-blue-600 mb-1">Calls Remaining</label>
                        <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                          {selectedCustomerAMC.callsRemaining}
                        </div>
                      </div>
                    )}
                    {selectedCustomerAMC.visitsRemaining !== null && (
                      <div>
                        <label className="block text-xs font-medium text-blue-600 mb-1">Visits Remaining</label>
                        <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded border">
                          {selectedCustomerAMC.visitsRemaining}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Tally Serial Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tally Serial Number
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.tallySerialNumber || 'Not Available'}
                    readOnly
                    placeholder="Auto-fetched from customer data"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-700 cursor-not-allowed"
                  />
                  {formData.tallySerialNumber && formData.customerId && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <span className="text-green-500 text-sm" title="Auto-fetched from customer">✅</span>
                    </div>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">Auto-fetched from customer data - not editable</p>
              </div>

              {/* Contact Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Number *
                </label>
                <div className="relative">
                  <MobileInput
                    value={formData.contactNumber}
                    onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                    placeholder="Contact number"
                    error={!!errors.contactNumber}
                    className="w-full"
                  />
                  {formData.contactNumber && formData.customerId && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <span className="text-green-500 text-sm" title="Auto-fetched from customer">✅</span>
                    </div>
                  )}
                </div>
                {errors.contactNumber && <p className="text-red-500 text-xs mt-1">{errors.contactNumber}</p>}
                <p className="text-xs text-gray-500 mt-1">Auto-fetched when customer is selected, editable for new leads</p>
              </div>

              {/* Executive Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Executive Name
                </label>
                <SearchableSelect
                  options={executives}
                  value={formData.executiveId}
                  onChange={(value) => handleInputChange('executiveId', value)}
                  placeholder="Search executives..."
                  searchFields={['first_name', 'last_name', 'name', 'employee_code']}
                  displayField="name"
                  valueField="id"
                  error={!!errors.executiveId}
                  minSearchLength={1}
                  renderOption={(executive, isHighlighted) => (
                    <div className={`px-4 py-3 cursor-pointer ${isHighlighted ? 'bg-blue-50' : 'hover:bg-gray-50'}`}>
                      <div className="font-medium text-gray-900">
                        {executive.name || `${executive.first_name || ''} ${executive.last_name || ''}`.trim()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {executive.employee_code} • {executive.department || 'No Department'}
                      </div>
                    </div>
                  )}
                />
                {errors.executiveId && <p className="text-red-500 text-xs mt-1">{errors.executiveId}</p>}
                <p className="text-xs text-gray-500 mt-1">Optional - assign an executive to this service call</p>
              </div>

              {/* Call Issue/Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Call Issue/Type
                </label>
                <SearchableSelect
                  options={productsIssues.map(item => ({
                    id: item.id,
                    name: item.name,
                    category: item.category,
                    description: item.description,
                    displayName: item.category ? `${item.category} - ${item.name}` : item.name
                  }))}
                  value={formData.typeOfCallId}
                  onChange={(value) => handleInputChange('typeOfCallId', value)}
                  placeholder="GST Doubts, Installation, Technical Support..."
                  searchFields={['name', 'category', 'description']}
                  displayField="displayName"
                  valueField="id"
                  error={!!errors.typeOfCallId}
                  groupBy="category"
                  minSearchLength={1}
                />
                {errors.typeOfCallId && <p className="text-red-500 text-xs mt-1">{errors.typeOfCallId}</p>}
                <p className="text-xs text-gray-500 mt-1">
                  Optional - select the specific type of issue or service required
                </p>
              </div>

              {/* Call Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Call Type
                </label>
                <select
                  value={formData.callType || ''}
                  onChange={(e) => handleInputChange('callType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select call type (optional)</option>
                  <option value="free_call">Free Call</option>
                  <option value="amc_call">AMC Call</option>
                  <option value="per_call">Per Call</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Optional - specify the call type for this service call</p>
              </div>



              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                  {statusUpdating && (
                    <span className="ml-2 text-xs text-blue-600">
                      <span className="animate-spin inline-block w-3 h-3 border border-blue-600 border-t-transparent rounded-full mr-1"></span>
                      Updating...
                    </span>
                  )}
                </label>
                <select
                  value={formData.statusId || ''}
                  onChange={(e) => handleInputChange('statusId', e.target.value)}
                  disabled={statusUpdating}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.statusId ? 'border-red-500' : 'border-gray-300'
                  } ${statusUpdating ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                >
                  <option value="">Select status...</option>
                  {callStatuses.map((status) => (
                    <option key={status.id} value={status.id}>
                      {status.name} {status.code && `(${status.code})`}
                    </option>
                  ))}
                </select>
                {errors.statusId && <p className="text-red-500 text-xs mt-1">{errors.statusId}</p>}
                {statusUpdating && (
                  <p className="text-blue-600 text-xs mt-1">
                    🔄 Updating status...
                  </p>
                )}
                {!statusUpdating && (
                  <p className="text-xs text-gray-500 mt-1">
                    Optional - defaults to "Open". Timer starts automatically when set to "In Progress"
                  </p>
                )}
              </div>

              {/* Company Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  placeholder="Auto-fetched from customer or enter manually"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">Auto-fetched from customer data, editable for leads</p>
              </div>


              {/* Map Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Map Location
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={formData.serviceLocation}
                    onChange={(e) => handleInputChange('serviceLocation', e.target.value)}
                    placeholder="Enter address or click map to select"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => setShowMapPicker(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <FaMapMarkerAlt />
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">Optional - specify location for service delivery</p>
              </div>
            </div>

            {/* Service Charges - Moved to bottom */}
            <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-800 mb-3 border-b border-gray-200 pb-2">
                Service Charges (Optional)
              </h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Service Charges
                </label>
                <input
                  type="number"
                  value={formData.serviceCharges}
                  onChange={(e) => handleInputChange('serviceCharges', e.target.value)}
                  placeholder="Enter service charges amount"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Optional - primarily for lead quotations
                </p>
              </div>
            </div>

            {/* Auto-populated Customer Information (Read-only) - Only in create mode */}
            {formData.customerId && (
              <div className="bg-gray-50 p-6 rounded-lg">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <FaBuilding className="mr-2" />
                  Customer Information (Auto-populated)
                  <div className="ml-auto flex items-center space-x-2">
                    {formData.tallyVersion && (
                      <span className="text-green-500 text-sm" title="Tally Version auto-fetched">✅ Tally Version</span>
                    )}
                    {formData.tssStatus && (
                      <span className="text-green-500 text-sm" title="TSS Status auto-fetched">✅ TSS Status</span>
                    )}
                    {(!formData.tallyVersion || !formData.tssStatus) && (
                      <span className="text-yellow-500 text-sm" title="Some data not available">⚠️ Partial Data</span>
                    )}
                    {formData.customerId && (
                      <button
                        type="button"
                        onClick={() => handleCustomerChange(formData.customerId)}
                        className="text-blue-500 hover:text-blue-700 text-sm flex items-center mr-2"
                        title="Refresh customer data"
                      >
                        🔄 Refresh
                      </button>
                    )}
                    {formData.customerId && process.env.NODE_ENV === 'development' && (
                      <button
                        type="button"
                        onClick={() => testCustomerAutoFetch(formData.customerId)}
                        className="text-purple-500 hover:text-purple-700 text-sm flex items-center"
                        title="Test auto-fetch functionality (Dev only)"
                      >
                        🧪 Test
                      </button>
                    )}
                  </div>
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Email Address */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={formData.emailAddress || 'Not Available'}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
                    />
                  </div>

                  {/* Designation */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Designation
                    </label>
                    <input
                      type="text"
                      value={formData.designation || 'Not Available'}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
                    />
                  </div>

                  {/* Tally Version */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tally Version
                    </label>
                    <input
                      type="text"
                      value={formData.tallyVersion || 'Not Available'}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
                    />
                  </div>

                  {/* TSS Status */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      TSS Status
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={formData.tssStatus || 'Not Available'}
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
                      />
                      {formData.tssStatus === 'active' && (
                        <span className="text-green-500 text-sm">✅</span>
                      )}
                      {formData.tssStatus === 'inactive' && (
                        <span className="text-red-500 text-sm">❌</span>
                      )}
                    </div>
                  </div>

                  {/* TSS Expiry */}
                  {formData.tssStatus === 'active' && formData.tssExpiry && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TSS Expiry
                      </label>
                      <input
                        type="text"
                        value={formData.tssExpiry ? new Date(formData.tssExpiry).toLocaleDateString() : 'Not Available'}
                        readOnly
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
                      />
                    </div>
                  )}
                </div>

                <p className="text-xs text-gray-500 mt-4">
                  ℹ️ This information is automatically populated from customer data and will be added to the service call record in the background.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Form submission buttons - Outside conditional rendering */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <button
            type="button"
            onClick={() => navigate('/services')}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <FaSave className="mr-2" />
                {isEdit ? 'Update Service' : 'Create Service'}
              </>
            )}
          </button>
        </div>
      </form>

      {/* Map Location Picker Modal */}
      <MapLocationPicker
        isOpen={showMapPicker}
        onClose={() => setShowMapPicker(false)}
        onLocationSelect={handleLocationSelect}
        initialLocation={formData.serviceLocation}
        initialLat={formData.locationCoordinates.lat}
        initialLng={formData.locationCoordinates.lng}
      />

      {/* Customer Feedback Modal */}
      <CustomerFeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        onSubmit={handleFeedbackSubmit}
        serviceData={{
          totalHours: calculateTotalHours(),
          totalAmount: calculateServiceAmount(),
          executiveRemarks: formData.executiveRemarks
        }}
      />

      {/* New Customer Modal */}
      <NewCustomerModal
        isOpen={showNewCustomerModal}
        onClose={() => setShowNewCustomerModal(false)}
        onCustomerCreated={handleCustomerCreated}
      />
    </div>
  );
};

export default EnhancedServiceForm;
