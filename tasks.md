
task1:

task2:

task3:

task4:
in the customer form have one is call type amc, free, per call if the user is slected the customer this should autofetch which call is selected when the user is created (good practice : you are good developed the autofetch free,per,amc call. issue : if the customer have a amc please show the amc expiry , renewal date, net visit like this undet the call type drop down input )

task 5: 

task6:
in the service report page there is showing defaultly all the services but i need only completed service reports only please change the default filete as complated servicesonly (isue the service report page aesvices section showing only one row only why? i wanna show completed calls there are totaly 192 completed calls but why that showing only one row only please check this and the cards should sow the correct valies which is total services 583, completed 192, open 391, like this please verify this and fi the issue)

task7:


task8:


